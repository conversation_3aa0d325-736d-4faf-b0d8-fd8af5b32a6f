import 'package:freezed_annotation/freezed_annotation.dart';

part 'recording_model.freezed.dart';
part 'recording_model.g.dart';

@freezed
class RecordingModel with _$RecordingModel {
  factory RecordingModel({
    @Json<PERSON>ey(name: '_id') required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'title') @Default('') String title,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'singer') @Default('') String singer,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'final_video_file_path') @Default('') String finalVideoFilePath,
    @<PERSON>son<PERSON>ey(name: 'final_mixed_audio_path') @Default('') String finalMixedAudioPath,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'genre') @Default('') String genre,
    @Json<PERSON><PERSON>(name: 'master_song_id') @Default('') String masterSongId,
    @<PERSON>son<PERSON>ey(name: 'thumbnail_path') @Default('') String thumbnailPath,
    @<PERSON>son<PERSON>ey(name: 'tempo') @Default('') String tempo,
    @Json<PERSON>ey(name: 'scale') @Default('') String scale,
    @Json<PERSON>ey(name: 'is_final_save') @Default(false) bool isFinalSave,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_deleted') @Default(false) bool isDeleted,
    @Json<PERSON>ey(name: 'media_type') @Default('audio') String mediaType,
    @Json<PERSON>ey(name: 'created_at') @Default(0) int createdAt,
    @JsonKey(name: 'feed_type') @Default('') String feedType,
    @JsonKey(name: 'lyrics_timeline_file_path') @Default('') String lyricsJsonPath,

  }) = _RecordingModel;

  factory RecordingModel.fromJson(Map<String, dynamic> json) => _$$RecordingModelImplFromJson(json);
}
