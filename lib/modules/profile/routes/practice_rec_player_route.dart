
import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/route_module.dart';

class PracticeRecordingPlayerRoutesPath {
  static const String practiceRecPlayer = 'practiceRecPlayer';
}

class PracticeRecordingPlayerRoutesModule implements RouteModule {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          path: PracticeRecordingPlayerRoutesPath.practiceRecPlayer,
          page: PracticeRecordingPlayerRoute.page,
        ),
      ];
}
