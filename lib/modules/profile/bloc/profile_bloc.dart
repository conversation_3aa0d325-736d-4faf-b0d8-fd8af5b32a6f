import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:melodyze/core/api_client/api_exception.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/services/file_manager/file_manager.dart';
import 'package:melodyze/core/services/user_data_manager.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/auth/model/melodyze_user.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/bloc/profile_state.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/profile_screen.dart';
import 'package:melodyze/modules/profile/repo/profile_repo.dart';

class ProfileBloc extends SafeBloc {
  final ProfileRepo profileRepo;
  List<RecordingModel> recordings = [];
  MelodyzeUser melodyzeUser = MelodyzeUser(id: "");
  final UserDataManager _userDataManager = DI().resolve<UserDataManager>();

  ProfileBloc({required this.profileRepo}) : super(InitialState()) {
    on<ProfileLoadDataEvent>(_loadProfileData);
    on<LoadRecordingsEvent>(_loadRecordings);
    on<DeleteRecordingEvent>(_deleteRecording);
    on<DeleteAccountEvent>(_deleteAccount);
    on<LogoutEvent>(_logout);
    on<ProfileMarkAsFinalEvent>(_markAsFinal);
    on<UpdateProfilePhotoEvent>(_updateProfilePhoto);
    on<RemoveProfilePhotoEvent>(_removeProfilePhoto);
    add(const ProfileLoadDataEvent());
    add(const LoadRecordingsEvent());
  }

  ProfileRecordingsUiModel prepareUiModel(bool showCurrentRecording) {
    final practiceRecordings = recordings.where((element) => !element.isFinalSave).toList();
    return ProfileRecordingsUiModel(
      finalRecordings: recordings.where((element) => element.isFinalSave).toList(),
      practiceRecordings: {
        for (final recording in practiceRecordings) recording.masterSongId: practiceRecordings.where((element) => element.masterSongId == recording.masterSongId).toList(),
      },
      showCurrentRecording: showCurrentRecording,
    );
  }

  Future<void> _updateUserData() async {
    melodyzeUser = await _userDataManager.getUserData() ?? MelodyzeUser(id: '');
    emit(const ProfileUpdatedState());
  }

  FutureOr<void> _loadProfileData(ProfileLoadDataEvent event, _) async {
    await _updateUserData();
  }

  FutureOr<void> _loadRecordings(LoadRecordingsEvent event, _) async {
    final response = await profileRepo.getRecordings();
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    recordings = response.data;
    emit(BlocSuccessState<ProfileRecordingsUiModel>(prepareUiModel(event.showCurrentRecording)));
  }

  FutureOr<void> _deleteRecording(DeleteRecordingEvent event, _) async {
    final response = await profileRepo.deleteRecording(event.recordingId);
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }

    if (recordings.isEmpty) {
      add(const LoadRecordingsEvent());
      return;
    }
    List<RecordingModel> tempRecordings = List.from(recordings);
    tempRecordings.removeWhere((element) => element.id == event.recordingId);
    recordings = tempRecordings;
    emit(BlocSuccessState<ProfileRecordingsUiModel>(prepareUiModel(false)));
  }

  FutureOr<void> _deleteAccount(DeleteAccountEvent event, _) async {
    emit(LoadingState());
    final response = await profileRepo.deleteAccount();
    if (!response.isSuccess) {
      emit(BlocFailureState(response.error));
      return;
    }
    recordings.clear();
    emit(const AccountDeletedState());
  }

  void _logout(LogoutEvent event, _) async {
    recordings.clear();
    emit(const LogOutClickedState());
  }

  Future<void> handleMenubarActions(BuildContext context, String action) async {
    switch (action) {
      case 'clear_cache':
        final result = await showYesNoDialog(
          context: context,
          title: 'Clear cache',
          subTitle: 'Are you sure you want to clear cache ?',
        );
        if (result && context.mounted) {
          // await FileManagerQuickAccess.clearCache();
        }
        break;
      case 'logout':
        final result = await showYesNoDialog(
          context: context,
          title: 'Logout',
          subTitle: 'Are you sure you want to logout ?',
        );
        if (result && context.mounted) {
          add(const LogoutEvent());
        }
        break;
      case 'delete_account':
        final result = await showYesNoDialog(
          context: context,
          title: 'Delete Account',
          subTitle: 'Are you sure you want to delete your account ?',
        );
        if (result && context.mounted) {
          add(const DeleteAccountEvent());
        }
        break;
    }
  }

  Future<void> _markAsFinal(ProfileMarkAsFinalEvent event, _) async {
    final response = await profileRepo.saveAsFinal(event.recording.id);
    if (response.isSuccess) {
      emit(ProfileSnackBarState(message: event.isFinal ? 'Recording marked as practice!' : 'Recording marked as final!'));
      add(const LoadRecordingsEvent());
    } else {
      emit(ProfileSnackBarState(
        message: 'Failed to mark as final: ${(response.error.e as ApiException).message}',
        isError: true,
      ));
      return;
    }
  }

  Future<void> _updateProfilePhoto(UpdateProfilePhotoEvent event, _) async {
    emit(const ProfilePhotoUploadingState());
    try {
      // Upload the image file using the existing file manager
      final uploadResult = await FileManagerQuickAccess.upload(
        event.imagePath,
        Endpoints.getUploadProfilePhotoSignedUrl,
        FileType.others,
      );

      // Clean up the cropped file
      try {
        await File(event.imagePath).delete();
      } catch (e) {
        logger.e('Failed to delete temporary cropped file', error: e);
      }

      if (uploadResult is FileOperationSuccess) {
        final updateResponse = await profileRepo.updateProfilePhoto(uploadResult.localPath);

        if (updateResponse.isSuccess) {
          await _userDataManager.updateUserData();
          // add(const ProfileLoadDataEvent());
          await _updateUserData();
          unawaited(DI().resolve<AppToast>().showToast("Profile photo updated"));
        } else {
          emit(ProfileSnackBarState(
            message: 'Failed to update profile photo: ${(updateResponse.error.e as ApiException).message}',
            isError: true,
          ));
        }
      } else {
        final error = uploadResult as FileOperationError;
        emit(ProfileSnackBarState(
          message: 'Failed to upload image: ${error.message}',
          isError: true,
        ));
      }
    } catch (e) {
      emit(ProfileSnackBarState(
        message: 'Failed to update profile photo: ${e.toString()}',
        isError: true,
      ));
    }
  }

  Future<void> _removeProfilePhoto(RemoveProfilePhotoEvent event, _) async {
    emit(const ProfilePhotoUploadingState());
    try {
      final response = await profileRepo.removeProfilePhoto();

      if (response.isSuccess) {
        await _userDataManager.updateUserData();
        // add(const ProfileLoadDataEvent());
        await _updateUserData();

        unawaited(DI().resolve<AppToast>().showToast("Profile photo removed"));
      } else {
        emit(ProfileSnackBarState(
          message: 'Failed to remove profile picture: ${(response.error.e as ApiException).message}',
          isError: true,
        ));
      }
    } catch (e) {
      emit(ProfileSnackBarState(
        message: 'Failed to remove profile picture: ${e.toString()}',
        isError: true,
      ));
    }
  }
}
