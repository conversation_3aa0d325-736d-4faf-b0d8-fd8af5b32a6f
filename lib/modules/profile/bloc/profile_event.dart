import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
class ProfileLoadDataEvent extends BaseEvent {
  const ProfileLoadDataEvent();

  @override
  List<Object> get props => [];
}

class LoadRecordingsEvent extends BaseEvent {
  final bool showCurrentRecording;
  const LoadRecordingsEvent({this.showCurrentRecording = false});

  @override
  List<Object> get props => [];
}

class DeleteRecordingEvent extends BaseEvent {
  final String recordingId;
  const DeleteRecordingEvent(this.recordingId);

  @override
  List<Object?> get props => [recordingId];
}

class DeleteAccountEvent extends BaseEvent {
  const DeleteAccountEvent();

  @override
  List<Object?> get props => [];
}

class LogoutEvent extends BaseEvent {
  const LogoutEvent();

  @override
  List<Object?> get props => [];
}

class ProfileMarkAsFinalEvent extends BaseEvent {
  final RecordingModel recording;
  final bool isFinal;
  const ProfileMarkAsFinalEvent({
    required this.recording,
    required this.isFinal,
  });

  @override
  List<Object?> get props => [recording];
}

class UpdateProfilePhotoEvent extends BaseEvent {
  final String imagePath;
  const UpdateProfilePhotoEvent(this.imagePath);

  @override
  List<Object?> get props => [imagePath];
}

class RemoveProfilePhotoEvent extends BaseEvent {
  const RemoveProfilePhotoEvent();

  @override
  List<Object?> get props => [];
}
