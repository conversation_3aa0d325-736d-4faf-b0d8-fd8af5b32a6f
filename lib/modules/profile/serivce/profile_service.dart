import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_service.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class ProfileService extends BaseService {
  
  Future<Map<String, dynamic>?> getProfile() async {
    return await DI().resolve<ApiClient>().get(Endpoints.getProfile);
  }

  Future<Map<String, dynamic>?> getRecordings() async {
    return await DI().resolve<ApiClient>().post(Endpoints.getRecordings, body: {"limit": Config.apiLimit, "skip": 0});
  }

  Future<Map<String, dynamic>?> deleteRecording(String recordingId) async {
    return await DI().resolve<ApiClient>().delete(Endpoints.deleteRecording, body: {"recording_id": recordingId});
  }

  Future<Map<String, dynamic>?> deleteAccount() async {
    return await DI().resolve<ApiClient>().delete(Endpoints.deleteAccount, body: {});
  }

  Future<Map<String, dynamic>?> saveAsFinal(String recordingId) async {
    return await DI().resolve<ApiClient>().patch(Endpoints.saveAsFinal, body: {"recording_id": recordingId});
  }

  Future<Map<String, dynamic>?> updateProfilePhoto(String path) async {
    return await DI().resolve<ApiClient>().put(Endpoints.updateProfilePhoto, body: {'profile_picture_path': path});
  }

  Future<Map<String, dynamic>?> removeProfilePhoto() async {
    return await DI().resolve<ApiClient>().patch(Endpoints.removeProfilePhoto, body: {});
  }
}
