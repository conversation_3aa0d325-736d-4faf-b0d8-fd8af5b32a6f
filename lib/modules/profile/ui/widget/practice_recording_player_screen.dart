import 'dart:async';
import 'package:flutter/material.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:just_audio/just_audio.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/atom/app_slider.dart';

@RoutePage()
class PracticeRecordingPlayerScreen extends StatefulWidget {
  final RecordingModel recording;

  const PracticeRecordingPlayerScreen({
    super.key,
    required this.recording,
  });

  @override
  State<PracticeRecordingPlayerScreen> createState() => _PracticeRecordingPlayerScreenState();
}

class _PracticeRecordingPlayerScreenState extends State<PracticeRecordingPlayerScreen> {
  late AudioPlayer _audioPlayer;
  int _playbackPositionMillis = 0;
  int _audioDurationMillis = 0;
  bool _isPlaying = false;
  LyricsData? _lyricsData;
  bool _lyricsLoaded = false;

  String get songtitle => FileUtils.fromSnakeCase(widget.recording.title.split('-').first);
  String get info => "${Config.keyMapShort[widget.recording.scale] ?? widget.recording.scale} · ${widget.recording.tempo} bpm";
  String get genre => widget.recording.genre;
  String get singer => widget.recording.singer.isNotEmpty ? widget.recording.singer : ' ';
  bool get isFinalRecording => widget.recording.isFinalSave;
  bool get showSongDetails => widget.recording.feedType != 'direct_upload';

  @override
  void initState() {
    super.initState();
    _initializeAudio();
    _fetchLyrics();
  }

  Future<void> _initializeAudio() async {
    _audioPlayer = AudioPlayer();
    await _audioPlayer.setLoopMode(LoopMode.one);
    await _audioPlayer.setUrl(widget.recording.finalMixedAudioPath);

    _audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        logger.d('Audio player state changed: playing=${state.playing}, processingState=${state.processingState}');
        setState(() {
          _isPlaying = state.playing;
        });
      }
    });

    _audioPlayer.positionStream.listen((position) {
      if (mounted) {
        setState(() {
          _playbackPositionMillis = position.inMilliseconds;
        });
      }
    });

    _audioPlayer.durationStream.listen((duration) {
      if (mounted && duration != null) {
        setState(() {
          _audioDurationMillis = duration.inMilliseconds;
        });
      }
    });

    unawaited(_audioPlayer.play());
  }

  Future<void> _fetchLyrics() async {
    if (_lyricsLoaded) return;

    try {
      if (widget.recording.lyricsJsonPath.isNotEmpty) {
        final response = await DI().resolve<ApiClient>().get(widget.recording.lyricsJsonPath);
        if (response != null) {
          final lyricsData = LyricsData.fromJson(response);
          if (mounted) {
            setState(() {
              _lyricsData = lyricsData;
              _lyricsLoaded = true;
            });
          }
          return;
        }
      }
    } catch (e) {
      logger.e('Failed to fetch lyrics: $e');
    }
  }

  void _togglePlay() {
    if (_isPlaying) {
      _audioPlayer.pause();
    } else {
      _audioPlayer.play();
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final lyricsList = _lyricsData?.lyrics.data.map((e) => e.text).toList() ?? [];

    // Get user profile information from ProfileBloc via DI
    final profileBloc = DI().resolve<ProfileBloc>();
    final user = profileBloc.melodyzeUser;
    final userProfileImageUrl = user.profilePicUrl ?? Config.noUserDP;
    final username = 'FT. ${user.username.split(' ').first}';

    return MeloScaffold(
      showBackground: false,
      showBackButton: true,
      extendBody: true,
      onBackPressed: () => Navigator.of(context).pop(),
      secondaryAction: (context) => PopupMenuButton<String>(
        icon: ShaderMask(
          shaderCallback: (bounds) => AppGradients.gradientPinkIcon.createShader(bounds),
          child: const Icon(Icons.more_vert, size: 28, color: Colors.white),
        ),
        color: Colors.transparent,
        itemBuilder: (BuildContext context) => [
          PopupMenuItem(
            padding: EdgeInsets.zero,
            child: AppGradientContainer(
              gradient: AppGradients.gradientBlackTeal,
              child: Column(
                children: [
                  PopupMenuItem(
                    value: isFinalRecording ? 'move_to_practice' : 'make_it_final',
                    child: ListTile(
                      leading: const Icon(
                        Icons.check,
                        color: AppColors.white,
                      ),
                      title: Text(
                        isFinalRecording ? 'Move to practice' : 'Make it final',
                        style: AppTextStyles.text16medium.copyWith(
                          fontFamily: AppFonts.iceland,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                    onTap: () async {
                      final result = await showYesNoDialog(context: context, title: isFinalRecording ? 'Move to practice' : 'Make it final');
                      if (result && context.mounted) {
                        DI().resolve<ProfileBloc>().add(ProfileMarkAsFinalEvent(recording: widget.recording, isFinal: isFinalRecording));
                      }
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(
                        Icons.delete_forever,
                        color: AppColors.white,
                      ),
                      title: Text(
                        'Delete',
                        style: AppTextStyles.text16medium.copyWith(
                          fontFamily: AppFonts.iceland,
                          color: AppColors.white,
                        ),
                      ),
                      onTap: () async {
                        final result = await showYesNoDialog(context: context, title: 'Delete recording');
                        if (result && context.mounted) {
                          unawaited(_audioPlayer.stop());
                          Navigator.pop(context);
                          await Future.delayed(const Duration(milliseconds: 100));
                          if (context.mounted) {
                            DI().resolve<ProfileBloc>().add(DeleteRecordingEvent(widget.recording.id));
                            context.router.pop();
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: ColoredBox(
        color: Colors.black,
        child: _lyricsLoaded
            ? _buildPlayerContent(context, lyricsList, userProfileImageUrl, username)
            : const Center(
                child: CircularProgressIndicator(
                  color: Colors.purpleAccent,
                ),
              ),
      ),
    );
  }

  Widget _buildPlayerContent(BuildContext context, List<String> lyrics, String userProfileImageUrl, String username) {
    return Stack(
      children: [
        // Main content - no SafeArea wrapper, positioned naturally
        Positioned.fill(
          child: Column(
            children: [
              // Add top padding to account for status bar and app bar
              SizedBox(height: MediaQuery.of(context).padding.top + 60),

              // Header section with profile info and song metadata
              _buildHeaderSection(userProfileImageUrl, username),
              const SizedBox(height: 40),

              // Wave animation with centered play/pause button
              _buildWavePlayerSection(),

              // Lyrics section
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: _LyricsDisplay(
                    lyrics: lyrics,
                    lyricsData: _lyricsData?.lyrics,
                    playbackPositionMillis: _playbackPositionMillis,
                    audioDurationMillis: _audioDurationMillis,
                  ),
                ),
              ),

              // Add bottom padding to account for positioned elements
              const SizedBox(height: 100),
            ],
          ),
        ),

        // Bottom black gradient - always visible for song metadata background
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, Colors.black],
              ),
            ),
          ),
        ),

        // Centered play/pause button overlay
        Center(
          child: GestureDetector(
            onTap: _togglePlay,
            child: SizedBox(
              width: 54,
              height: 54,
              child: ImageLoader.fromAsset(
                _isPlaying ? AssetPaths.pauseReel : AssetPaths.playReel,
              ),
            ),
          ),
        ),

        // Song details positioned at bottom (above nav bar)
        Positioned(
          bottom: 32,
          left: 20,
          right: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showSongDetails)
                Row(
                  children: [
                    const SizedBox(width: 10),
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Image.asset(
                                AssetPaths.musicNote,
                                width: 16,
                                height: 22,
                                color: AppColors.white,
                              ),
                              const SizedBox(width: 6),
                              SizedBox(
                                width: MediaQuery.sizeOf(context).width * 0.5,
                                child: Marqueee(
                                  text: "$songtitle · ${widget.recording.genre}",
                                  style: AppTextStyles.text16regular.copyWith(
                                    fontFamily: AppFonts.inter,
                                  ),
                                  width: 250,
                                ),
                              ),
                            ],
                          ),
                          if (singer.isNotEmpty)
                            Marqueee(
                              text: singer,
                              style: AppTextStyles.text14regular.copyWith(fontFamily: AppFonts.iceland),
                              width: 220,
                            ),
                          if (singer.isEmpty)
                            Flex(
                              direction: Axis.horizontal,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Marqueee(
                                  text: info,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: AppFonts.iceland,
                                  ),
                                  width: 250,
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),

        // App slider positioned at bottom - outside SafeArea
        Positioned(
          bottom: 0,
          width: MediaQuery.sizeOf(context).width,
          child: AppSlider(
            value: _playbackPositionMillis.toDouble(),
            max: _audioDurationMillis > 0 ? _audioDurationMillis.toDouble() : 100.0,
            onChanged: (value) {
              _audioPlayer.seek(Duration(milliseconds: value.toInt()));
            },
            gestureHitAreaHeight: 12,
            activeColor: AppColors.white,
            isRounded: false,
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderSection(String userProfileImageUrl, String username) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(30.0, 20.0, 24.0, 24.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile image stack
          _buildProfileImage(userProfileImageUrl),
          const SizedBox(width: 24),
          // Recording info with song metadata
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        songtitle,
                        style: AppTextStyles.textEthnocentricStyle.copyWith(
                          fontSize: 18,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.clip,
                      ),
                    ),
                  ],
                ),
                // Singer with marquee
                if (singer.isNotEmpty)
                  Text(
                    singer,
                    style: AppTextStyles.text16regular.copyWith(
                      fontFamily: AppFonts.iceland,
                      color: Colors.grey[400],
                    ),
                  ),
                const SizedBox(height: 10),
                // Username
                Text(
                  username,
                  style: AppTextStyles.text18regular.copyWith(
                    fontFamily: AppFonts.iceland,
                    color: Colors.grey[400],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImage(String userProfileImageUrl) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        SizedBox(
          width: 80,
          height: 80,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                AssetPaths.profileBorder,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
              ClipOval(
                child: SizedBox(
                  width: 75,
                  height: 75,
                  child: ImageLoader.network(
                    userProfileImageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[800],
                        child: const Icon(Icons.person, color: Colors.white, size: 48),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          child: SizedBox(
            width: 40,
            height: 40,
            child: Image.asset(
              AssetPaths.catComplete,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWavePlayerSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0),
      child: SizedBox(
        height: 120,
        child: _isPlaying
            ? Image.asset(
                '${AssetPaths.gifPath}/wave_video.gif',
                fit: BoxFit.cover,
                width: double.infinity,
                height: 120,
              )
            : Image.asset(
                '${AssetPaths.pngPath}/wave_static.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: 120,
              ),
      ),
    );
  }
}

class _LyricsDisplay extends StatefulWidget {
  final List<String> lyrics;
  final LyricsList? lyricsData;
  final int playbackPositionMillis;
  final int audioDurationMillis;

  const _LyricsDisplay({
    required this.lyrics,
    required this.lyricsData,
    required this.playbackPositionMillis,
    required this.audioDurationMillis,
  });

  @override
  State<_LyricsDisplay> createState() => _LyricsDisplayState();
}

class _LyricsDisplayState extends State<_LyricsDisplay> {
  int _parseTimeToMillis(String time) {
    final parts = time.split(':');
    if (parts.length == 3) {
      final min = int.tryParse(parts[0]) ?? 0;
      final sec = int.tryParse(parts[1]) ?? 0;
      final ms = int.tryParse(parts[2]) ?? 0;
      return min * 60000 + sec * 1000 + ms;
    }
    return 0;
  }

  int _getCurrentLyricIndex() {
    if (widget.lyricsData == null || widget.lyrics.isEmpty) return 0;

    final data = widget.lyricsData!.data;
    if (data.isEmpty) return 0;

    // Handle looping: get position within the lyrics duration
    int adjustedPosition = widget.playbackPositionMillis;
    if (widget.audioDurationMillis > 0) {
      // Calculate lyrics duration
      final lastLyricEnd = data.isNotEmpty ? _parseTimeToMillis(data.last.endTime) : 0;
      final lyricsDuration = lastLyricEnd > 0 ? lastLyricEnd : widget.audioDurationMillis;

      // Get position within one loop cycle
      adjustedPosition = widget.playbackPositionMillis % lyricsDuration;
    }

    // Find current lyric based on timing
    for (int i = 0; i < data.length; i++) {
      final start = _parseTimeToMillis(data[i].startTime);
      final end = _parseTimeToMillis(data[i].endTime);

      if (end == 0) {
        // No end time specified, check if we're past start
        if (adjustedPosition >= start) {
          // Check if this is the last lyric or if next lyric hasn't started
          if (i == data.length - 1) return i;
          final nextStart = _parseTimeToMillis(data[i + 1].startTime);
          if (adjustedPosition < nextStart) return i;
        }
      } else {
        // End time specified
        if (adjustedPosition >= start && adjustedPosition < end) {
          return i;
        }
      }
    }

    // Default fallback
    return adjustedPosition < _parseTimeToMillis(data[0].startTime) ? 0 : data.length - 1;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.lyrics.isEmpty) {
      return Center(
        child: Text(
          'No lyrics available',
          style: AppTextStyles.text16regular.copyWith(
            color: Colors.grey,
          ),
        ),
      );
    }

    final currentIndex = _getCurrentLyricIndex();

    return SizedBox(
      height: 220,
      width: 280,
      child: Stack(
        children: [
          // Main lyrics column
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Previous lyric
              _buildLyricLine(
                text: currentIndex > 0 ? widget.lyrics[currentIndex - 1] : '',
                isCurrent: false,
                isVisible: currentIndex > 0,
                applyMask: false, // No mask in text itself
              ),

              // Current lyric (highlighted)
              _buildLyricLine(
                text: widget.lyrics[currentIndex],
                isCurrent: true,
                isVisible: true,
                applyMask: false, // No mask for current lyric
              ),

              // Next lyric
              _buildLyricLine(
                text: currentIndex < widget.lyrics.length - 1 ? widget.lyrics[currentIndex + 1] : '',
                isCurrent: false,
                isVisible: currentIndex < widget.lyrics.length - 1,
                applyMask: false, // No mask in text itself
              ),
            ],
          ),

          // Top black mask overlay - positioned over the top lyric
          if (currentIndex > 0)
            Positioned(
              top: 20,
              left: 0,
              right: 0,
              height: 120, // Increased height of one lyric line mask
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

          // Bottom black mask overlay - positioned over the bottom lyric
          if (currentIndex < widget.lyrics.length - 1)
            Positioned(
              bottom: 30,
              left: 0,
              right: 0,
              height: 100, // Increased height of one lyric line mask
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLyricLine({
    required String text,
    required bool isCurrent,
    required bool isVisible,
    bool applyMask = false,
  }) {
    if (!isVisible || text.isEmpty) {
      return const SizedBox(height: 72);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Center(
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: AppTextStyles.textEthnocentricStyle.copyWith(
            fontSize: isCurrent ? 16 : 12,
            color: isCurrent ? const Color(0xFFEBC0E8) : Colors.white,
            fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
